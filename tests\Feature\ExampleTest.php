<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that the contact page loads successfully.
     * This is a simpler test that doesn't require complex database content.
     */
    public function test_contact_page_loads_successfully(): void
    {
        $response = $this->get('/contact');

        $response->assertStatus(200);
        $response->assertSee('Contact');
    }

    /**
     * Test that the homepage loads successfully with seeded content.
     */
    public function test_homepage_loads_with_content(): void
    {
        // Seed the database with required content
        $this->seed(\Database\Seeders\ContentSeeder::class);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('Building Your Dreams with Excellence');
    }
}
