@extends('layouts.admin')

@section('page-title', 'Content Management')

@push('styles')
<style>
    .content-management {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: calc(100vh - 140px);
        padding: 2rem 0;
    }

    .content-header {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid rgba(255,183,3,0.1);
    }

    .content-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, #e6a503 100%);
        color: var(--secondary-color);
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .stat-card .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .search-filter-section {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 15px rgba(0,0,0,0.06);
    }

    .search-box {
        position: relative;
        margin-bottom: 1rem;
    }

    .search-box input {
        padding-left: 3rem;
        border-radius: 12px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .search-box input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(255,183,3,0.25);
    }

    .search-box .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .filter-chips {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .filter-chip {
        background: var(--primary-color);
        color: var(--secondary-color);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-chip .remove-filter {
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.3s ease;
    }

    .filter-chip .remove-filter:hover {
        opacity: 1;
    }

    .content-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .content-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 2px 15px rgba(0,0,0,0.06);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .content-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        border-color: var(--primary-color);
    }

    .content-card-header {
        display: flex;
        justify-content: between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .content-type-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .type-text { background: #e3f2fd; color: #1976d2; }
    .type-textarea { background: #f3e5f5; color: #7b1fa2; }
    .type-html { background: #e8f5e8; color: #388e3c; }
    .type-image { background: #fff3e0; color: #f57c00; }
    .type-json { background: #fce4ec; color: #c2185b; }

    .content-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .content-key {
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        color: #6c757d;
        background: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        margin-bottom: 1rem;
        word-break: break-all;
    }

    .content-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .meta-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .page-badge { background: #e3f2fd; color: #1976d2; }
    .section-badge { background: #f3e5f5; color: #7b1fa2; }

    .content-preview {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #495057;
        max-height: 80px;
        overflow: hidden;
        position: relative;
    }

    .content-preview::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(transparent, #f8f9fa);
    }

    .content-actions {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
    }

    .status-active .status-dot { background: #28a745; }
    .status-inactive .status-dot { background: #dc3545; }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn-action {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        background: var(--primary-color);
        color: var(--secondary-color);
        border-color: var(--primary-color);
    }

    .btn-action.btn-edit:hover {
        background: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }

    .btn-action.btn-delete:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 16px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.06);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 0.5rem;
    }

    .empty-state-text {
        color: #6c757d;
        margin-bottom: 2rem;
    }

    .floating-add-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color) 0%, #e6a503 100%);
        color: var(--secondary-color);
        border: none;
        box-shadow: 0 4px 20px rgba(255,183,3,0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .floating-add-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(255,183,3,0.6);
    }

    @media (max-width: 768px) {
        .content-grid {
            grid-template-columns: 1fr;
        }

        .content-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .floating-add-btn {
            bottom: 1rem;
            right: 1rem;
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }
    }
</style>
@endpush

@section('content')
<div class="content-management">
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="content-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h2 class="mb-1">Content Management</h2>
                    <p class="text-muted mb-0">Manage your website content with ease</p>
                </div>
                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addContentModal">
                    <i class="fas fa-plus me-2"></i>Add Content
                </button>
            </div>

            <!-- Stats Cards -->
            <div class="content-stats">
                <div class="stat-card">
                    <div class="stat-number">{{ $contents->total() }}</div>
                    <div class="stat-label">Total Content</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $contents->where('is_active', true)->count() }}</div>
                    <div class="stat-label">Active Content</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $pages->count() }}</div>
                    <div class="stat-label">Pages</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $sections->count() }}</div>
                    <div class="stat-label">Sections</div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="search-filter-section">
            <div class="row">
                <div class="col-lg-6">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control form-control-lg" placeholder="Search content by title or key..." id="searchInput" value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-select form-select-lg" id="pageFilter">
                                <option value="">All Pages</option>
                                @foreach($pages as $page)
                                    <option value="{{ $page }}" {{ request('page') == $page ? 'selected' : '' }}>
                                        {{ ucfirst($page) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select form-select-lg" id="sectionFilter">
                                <option value="">All Sections</option>
                                @foreach($sections as $section)
                                    <option value="{{ $section }}" {{ request('section') == $section ? 'selected' : '' }}>
                                        {{ ucfirst($section) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-outline-secondary btn-lg w-100" onclick="clearFilters()">
                                <i class="fas fa-times me-1"></i>Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Filters -->
            <div class="filter-chips" id="activeFilters">
                @if(request('search'))
                    <div class="filter-chip">
                        <i class="fas fa-search"></i>
                        Search: "{{ request('search') }}"
                        <span class="remove-filter" onclick="removeFilter('search')">×</span>
                    </div>
                @endif
                @if(request('page'))
                    <div class="filter-chip">
                        <i class="fas fa-file"></i>
                        Page: {{ ucfirst(request('page')) }}
                        <span class="remove-filter" onclick="removeFilter('page')">×</span>
                    </div>
                @endif
                @if(request('section'))
                    <div class="filter-chip">
                        <i class="fas fa-layer-group"></i>
                        Section: {{ ucfirst(request('section')) }}
                        <span class="remove-filter" onclick="removeFilter('section')">×</span>
                    </div>
                @endif
            </div>
        </div>

        <!-- Content Grid -->
        @if($contents->count() > 0)
            <div class="content-grid">
                @foreach($contents as $content)
                    <div class="content-card">
                        <!-- Content Type Badge -->
                        <div class="content-type-badge type-{{ $content->type }}">
                            {{ $content->type }}
                        </div>

                        <!-- Content Header -->
                        <div class="content-card-header">
                            <div class="flex-grow-1">
                                <h3 class="content-title">{{ $content->title }}</h3>
                                @if($content->description)
                                    <p class="text-muted mb-0" style="font-size: 0.9rem;">{{ Str::limit($content->description, 80) }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Content Key -->
                        <div class="content-key">{{ $content->key }}</div>

                        <!-- Meta Information -->
                        <div class="content-meta">
                            <span class="meta-badge page-badge">
                                <i class="fas fa-file me-1"></i>{{ ucfirst($content->page) }}
                            </span>
                            @if($content->section)
                                <span class="meta-badge section-badge">
                                    <i class="fas fa-layer-group me-1"></i>{{ ucfirst($content->section) }}
                                </span>
                            @endif
                        </div>

                        <!-- Content Preview -->
                        @if($content->content)
                            <div class="content-preview">
                                @if($content->type === 'html')
                                    {!! Str::limit(strip_tags($content->content), 120) !!}
                                @elseif($content->type === 'json')
                                    <code>{{ Str::limit($content->content, 120) }}</code>
                                @else
                                    {{ Str::limit($content->content, 120) }}
                                @endif
                            </div>
                        @endif

                        <!-- Content Actions -->
                        <div class="content-actions">
                            <div class="status-indicator {{ $content->is_active ? 'status-active' : 'status-inactive' }}">
                                <div class="status-dot"></div>
                                {{ $content->is_active ? 'Active' : 'Inactive' }}
                            </div>
                            <div class="action-buttons">
                                <button type="button" class="btn-action btn-edit" onclick="editContent({{ $content->id }})" title="Edit Content">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn-action btn-delete" onclick="deleteContent({{ $content->id }})" title="Delete Content">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($contents->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $contents->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3 class="empty-state-title">No Content Found</h3>
                <p class="empty-state-text">
                    @if(request()->hasAny(['search', 'page', 'section']))
                        No content matches your current filters. Try adjusting your search criteria.
                    @else
                        You haven't created any content yet. Start by adding your first content item.
                    @endif
                </p>
                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addContentModal">
                    <i class="fas fa-plus me-2"></i>Add Your First Content
                </button>
            </div>
        @endif

        <!-- Floating Add Button (Mobile) -->
        <button type="button" class="floating-add-btn d-md-none" data-bs-toggle="modal" data-bs-target="#addContentModal">
            <i class="fas fa-plus"></i>
        </button>
    </div>
</div>

<!-- Add Content Modal -->
<div class="modal fade" id="addContentModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" style="border-radius: 16px; border: none;">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color) 0%, #e6a503 100%); color: var(--secondary-color); border-radius: 16px 16px 0 0;">
                <div>
                    <h4 class="modal-title mb-1">
                        <i class="fas fa-plus-circle me-2"></i>Add New Content
                    </h4>
                    <p class="mb-0 opacity-75">Create a new content item for your website</p>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addContentForm">
                <div class="modal-body" style="padding: 2rem;">
                    <!-- Basic Information -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="add_title" class="form-label fw-semibold">Title *</label>
                                    <input type="text" class="form-control form-control-lg" id="add_title" name="title" required
                                           style="border-radius: 12px;" placeholder="Enter content title">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="add_key" class="form-label fw-semibold">Key *</label>
                                    <input type="text" class="form-control form-control-lg" id="add_key" name="key" required
                                           style="border-radius: 12px; font-family: 'Courier New', monospace;" placeholder="Auto-generated from title">
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1"></i>Unique identifier (auto-generated from title)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location & Type -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>Location & Type
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="add_page" class="form-label fw-semibold">Page *</label>
                                    <select class="form-select form-select-lg" id="add_page" name="page" required style="border-radius: 12px;">
                                        <option value="">Select Page</option>
                                        <option value="homepage">🏠 Homepage</option>
                                        <option value="about">ℹ️ About</option>
                                        <option value="contact">📞 Contact</option>
                                        <option value="services">🔧 Services</option>
                                        <option value="projects">🏗️ Projects</option>
                                        <option value="general">📄 General</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="add_section" class="form-label fw-semibold">Section</label>
                                    <input type="text" class="form-control form-control-lg" id="add_section" name="section"
                                           style="border-radius: 12px;" placeholder="e.g., hero, services, footer">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>Optional section grouping
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="add_type" class="form-label fw-semibold">Content Type *</label>
                                    <select class="form-select form-select-lg" id="add_type" name="type" required style="border-radius: 12px;">
                                        <option value="text">📝 Text</option>
                                        <option value="textarea">📄 Textarea</option>
                                        <option value="html">🌐 HTML</option>
                                        <option value="image">🖼️ Image</option>
                                        <option value="json">⚙️ JSON</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-edit me-2"></i>Content
                        </h6>
                        <div class="mb-3">
                            <label for="add_content" class="form-label fw-semibold">Content</label>
                            <textarea class="form-control" id="add_content" name="content" rows="5"
                                      style="border-radius: 12px;" placeholder="Enter your content here..."></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>The actual content that will be displayed on your website
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="add_description" class="form-label fw-semibold">Description</label>
                            <input type="text" class="form-control form-control-lg" id="add_description" name="description"
                                   style="border-radius: 12px;" placeholder="Brief description for admin reference">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Help text for admin users (not displayed on website)
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-cog me-2"></i>Settings
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="add_sort_order" class="form-label fw-semibold">Sort Order</label>
                                    <input type="number" class="form-control form-control-lg" id="add_sort_order" name="sort_order"
                                           value="0" min="0" style="border-radius: 12px;">
                                    <div class="form-text">
                                        <i class="fas fa-sort me-1"></i>Lower numbers appear first
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Status</label>
                                    <div class="form-check form-switch" style="padding-top: 0.5rem;">
                                        <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked
                                               style="transform: scale(1.2);">
                                        <label class="form-check-label fw-semibold" for="add_is_active">
                                            <span class="text-success">Active</span>
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-eye me-1"></i>Only active content is displayed on the website
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 1.5rem 2rem; background: #f8f9fa; border-radius: 0 0 16px 16px;">
                    <button type="button" class="btn btn-outline-secondary btn-lg" data-bs-dismiss="modal" style="border-radius: 12px;">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-lg" style="border-radius: 12px;">
                        <i class="fas fa-plus me-2"></i>Add Content
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Content Modal -->
<div class="modal fade" id="editContentModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content" style="border-radius: 16px; border: none;">
            <div class="modal-header" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border-radius: 16px 16px 0 0;">
                <div>
                    <h4 class="modal-title mb-1">
                        <i class="fas fa-edit me-2"></i>Edit Content
                    </h4>
                    <p class="mb-0 opacity-75">Update your content item</p>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editContentForm">
                <input type="hidden" id="edit_content_id" name="content_id">
                <div class="modal-body" style="padding: 2rem;">
                    <!-- Basic Information -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_title" class="form-label fw-semibold">Title *</label>
                                    <input type="text" class="form-control form-control-lg" id="edit_title" name="title" required
                                           style="border-radius: 12px;" placeholder="Enter content title">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_key" class="form-label fw-semibold">Key *</label>
                                    <input type="text" class="form-control form-control-lg" id="edit_key" name="key" required
                                           style="border-radius: 12px; font-family: 'Courier New', monospace;" placeholder="Unique identifier">
                                    <div class="form-text">
                                        <i class="fas fa-lightbulb me-1"></i>Unique identifier for this content
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location & Type -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>Location & Type
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_page" class="form-label fw-semibold">Page *</label>
                                    <select class="form-select form-select-lg" id="edit_page" name="page" required style="border-radius: 12px;">
                                        <option value="homepage">🏠 Homepage</option>
                                        <option value="about">ℹ️ About</option>
                                        <option value="contact">📞 Contact</option>
                                        <option value="services">🔧 Services</option>
                                        <option value="projects">🏗️ Projects</option>
                                        <option value="general">📄 General</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_section" class="form-label fw-semibold">Section</label>
                                    <input type="text" class="form-control form-control-lg" id="edit_section" name="section"
                                           style="border-radius: 12px;" placeholder="e.g., hero, services, footer">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>Optional section grouping
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_type" class="form-label fw-semibold">Content Type *</label>
                                    <select class="form-select form-select-lg" id="edit_type" name="type" required style="border-radius: 12px;">
                                        <option value="text">📝 Text</option>
                                        <option value="textarea">📄 Textarea</option>
                                        <option value="html">🌐 HTML</option>
                                        <option value="image">🖼️ Image</option>
                                        <option value="json">⚙️ JSON</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-edit me-2"></i>Content
                        </h6>
                        <div class="mb-3">
                            <label for="edit_content" class="form-label fw-semibold">Content</label>
                            <textarea class="form-control" id="edit_content" name="content" rows="5"
                                      style="border-radius: 12px;" placeholder="Enter your content here..."></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>The actual content that will be displayed on your website
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label fw-semibold">Description</label>
                            <input type="text" class="form-control form-control-lg" id="edit_description" name="description"
                                   style="border-radius: 12px;" placeholder="Brief description for admin reference">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>Help text for admin users (not displayed on website)
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="mb-4">
                        <h6 class="text-muted text-uppercase fw-bold mb-3">
                            <i class="fas fa-cog me-2"></i>Settings
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_sort_order" class="form-label fw-semibold">Sort Order</label>
                                    <input type="number" class="form-control form-control-lg" id="edit_sort_order" name="sort_order"
                                           min="0" style="border-radius: 12px;">
                                    <div class="form-text">
                                        <i class="fas fa-sort me-1"></i>Lower numbers appear first
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Status</label>
                                    <div class="form-check form-switch" style="padding-top: 0.5rem;">
                                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active"
                                               style="transform: scale(1.2);">
                                        <label class="form-check-label fw-semibold" for="edit_is_active">
                                            <span class="text-success">Active</span>
                                        </label>
                                    </div>
                                    <div class="form-text">
                                        <i class="fas fa-eye me-1"></i>Only active content is displayed on the website
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="padding: 1.5rem 2rem; background: #f8f9fa; border-radius: 0 0 16px 16px;">
                    <button type="button" class="btn btn-outline-secondary btn-lg" data-bs-dismiss="modal" style="border-radius: 12px;">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-info btn-lg" style="border-radius: 12px;">
                        <i class="fas fa-save me-2"></i>Update Content
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Enhanced search and filter functionality with debouncing
let searchTimeout;

document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        filterContent();
    }, 500); // Debounce search for 500ms
});

document.getElementById('pageFilter').addEventListener('change', function() {
    filterContent();
});

document.getElementById('sectionFilter').addEventListener('change', function() {
    filterContent();
});

function filterContent() {
    const search = document.getElementById('searchInput').value;
    const page = document.getElementById('pageFilter').value;
    const section = document.getElementById('sectionFilter').value;

    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (page) params.append('page', page);
    if (section) params.append('section', section);

    // Show loading state
    showLoadingState();

    window.location.href = '{{ route("admin.content") }}?' + params.toString();
}

function clearFilters() {
    showLoadingState();
    window.location.href = '{{ route("admin.content") }}';
}

function removeFilter(filterType) {
    const params = new URLSearchParams(window.location.search);
    params.delete(filterType);

    showLoadingState();
    window.location.href = '{{ route("admin.content") }}?' + params.toString();
}

function showLoadingState() {
    // Add loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.innerHTML = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.8); z-index: 9999; display: flex; align-items: center; justify-content: center;">
            <div style="text-align: center;">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading content...</p>
            </div>
        </div>
    `;
    document.body.appendChild(overlay);
}

// Enhanced Add Content Form with better UX
document.getElementById('addContentForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Content...';
    submitBtn.disabled = true;

    // Disable form inputs
    const formInputs = this.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => input.disabled = true);

    const formData = new FormData(this);

    try {
        const response = await fetch('{{ route("admin.content.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            // Show success message
            showNotification('Content created successfully!', 'success');

            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('addContentModal')).hide();

            // Reset form
            this.reset();

            // Reload page after short delay
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification(result.message || 'Error creating content', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        formInputs.forEach(input => input.disabled = false);
    }
});

// Enhanced Edit Content with loading state
async function editContent(id) {
    // Show loading state
    const loadingToast = showNotification('Loading content...', 'info', false);

    try {
        const response = await fetch(`/admin/content/${id}/edit`);
        const content = await response.json();

        if (response.ok) {
            // Populate edit form
            document.getElementById('edit_content_id').value = content.id;
            document.getElementById('edit_title').value = content.title;
            document.getElementById('edit_key').value = content.key;
            document.getElementById('edit_page').value = content.page;
            document.getElementById('edit_section').value = content.section || '';
            document.getElementById('edit_type').value = content.type;
            document.getElementById('edit_content').value = content.content || '';
            document.getElementById('edit_description').value = content.description || '';
            document.getElementById('edit_sort_order').value = content.sort_order;
            document.getElementById('edit_is_active').checked = content.is_active;

            // Hide loading toast
            if (loadingToast) loadingToast.hide();

            // Show modal
            new bootstrap.Modal(document.getElementById('editContentModal')).show();
        } else {
            throw new Error(content.message || 'Failed to load content');
        }
    } catch (error) {
        if (loadingToast) loadingToast.hide();
        showNotification('Error loading content data', 'error');
    }
}

// Enhanced Edit Content Form
document.getElementById('editContentForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Content...';
    submitBtn.disabled = true;

    // Disable form inputs
    const formInputs = this.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => input.disabled = true);

    const formData = new FormData(this);
    const contentId = document.getElementById('edit_content_id').value;

    try {
        const response = await fetch(`/admin/content/${contentId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            // Show success message
            showNotification('Content updated successfully!', 'success');

            // Hide modal
            bootstrap.Modal.getInstance(document.getElementById('editContentModal')).hide();

            // Reload page after short delay
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification(result.message || 'Error updating content', 'error');
        }
    } catch (error) {
        showNotification('Network error. Please try again.', 'error');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        formInputs.forEach(input => input.disabled = false);
    }
});

// Enhanced Delete Content with better confirmation
async function deleteContent(id) {
    // Create custom confirmation modal
    const confirmed = await showConfirmDialog(
        'Delete Content',
        'Are you sure you want to delete this content item? This action cannot be undone.',
        'Delete',
        'danger'
    );

    if (!confirmed) return;

    // Show loading state
    const loadingToast = showNotification('Deleting content...', 'info', false);

    try {
        const response = await fetch(`/admin/content/${id}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();

        if (response.ok) {
            if (loadingToast) loadingToast.hide();
            showNotification('Content deleted successfully!', 'success');

            // Reload page after short delay
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            throw new Error(result.message || 'Failed to delete content');
        }
    } catch (error) {
        if (loadingToast) loadingToast.hide();
        showNotification('Error deleting content', 'error');
    }
}

// Enhanced auto-generate key from title
document.getElementById('add_title').addEventListener('input', function() {
    const title = this.value;
    const key = title.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_')
        .replace(/^_+|_+$/g, '');

    if (key) {
        const page = document.getElementById('add_page').value || 'homepage';
        const section = document.getElementById('add_section').value;
        let generatedKey = page;
        if (section) generatedKey += '_' + section;
        generatedKey += '_' + key;

        document.getElementById('add_key').value = generatedKey;
    }
});

// Update key when page or section changes
document.getElementById('add_page').addEventListener('change', updateGeneratedKey);
document.getElementById('add_section').addEventListener('input', updateGeneratedKey);

function updateGeneratedKey() {
    const title = document.getElementById('add_title').value;
    if (title) {
        document.getElementById('add_title').dispatchEvent(new Event('input'));
    }
}

// Utility function for notifications
function showNotification(message, type = 'info', autoHide = true) {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toastId = 'toast_' + Date.now();
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    const colorMap = {
        success: 'text-success',
        error: 'text-danger',
        warning: 'text-warning',
        info: 'text-info'
    };

    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="${iconMap[type]} ${colorMap[type]} me-2"></i>
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { autohide: autoHide, delay: 5000 });

    toast.show();

    // Clean up after toast is hidden
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });

    return toast;
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Utility function for confirmation dialogs
function showConfirmDialog(title, message, confirmText = 'Confirm', type = 'primary') {
    return new Promise((resolve) => {
        const modalId = 'confirmModal_' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content" style="border-radius: 12px;">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-${type}" id="confirmBtn">${confirmText}</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);

        modalElement.querySelector('#confirmBtn').addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
            resolve(false);
        });

        modal.show();
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Remove loading overlay if it exists
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }

    // Add smooth animations to cards
    const cards = document.querySelectorAll('.content-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate__animated', 'animate__fadeInUp');
    });
});
</script>
@endpush
